/**
 * Test script for async LLM queue functionality
 * This script demonstrates how the new async system works
 */

import { queueManager, queueLLMProcessing, isJobComplete, waitForJobCompletion } from './src/utils/queueManager.js';

// Mock test data
const testPayload = {
  issueId: 'TEST-123-ID',
  issueKey: 'TEST-123',
  simpleRequirements: 'Create a user login system with authentication',
  triggerContext: {
    eventId: 'test-event-123',
    installationId: 'test-installation',
    timestamp: new Date().toISOString()
  }
};

/**
 * Test basic queue functionality
 */
async function testBasicQueue() {
  console.log('=== Testing Basic Queue Functionality ===');
  
  try {
    // Test queuing a request
    console.log('1. Queuing LLM processing request...');
    const jobId = await queueLLMProcessing(
      testPayload.issueId,
      testPayload.issueKey,
      testPayload.simpleRequirements,
      testPayload.triggerContext
    );
    
    console.log(`✅ Successfully queued job: ${jobId}`);
    
    // Test job status checking
    console.log('2. Checking job status...');
    const status = await queueManager.getJobStatus(jobId);
    console.log(`✅ Job status retrieved:`, status);
    
    return jobId;
    
  } catch (error) {
    console.error('❌ Basic queue test failed:', error);
    throw error;
  }
}

/**
 * Test batch processing
 */
async function testBatchProcessing() {
  console.log('\n=== Testing Batch Processing ===');
  
  try {
    const batchPayloads = [
      {
        issueId: 'BATCH-1-ID',
        issueKey: 'BATCH-1',
        simpleRequirements: 'Implement user registration'
      },
      {
        issueId: 'BATCH-2-ID', 
        issueKey: 'BATCH-2',
        simpleRequirements: 'Add password reset functionality'
      },
      {
        issueId: 'BATCH-3-ID',
        issueKey: 'BATCH-3', 
        simpleRequirements: 'Create user profile management'
      }
    ];
    
    console.log(`1. Queuing batch of ${batchPayloads.length} requests...`);
    const batchJobId = await queueManager.queueBatchLLMRequests(batchPayloads);
    
    console.log(`✅ Successfully queued batch job: ${batchJobId}`);
    
    // Check batch status
    console.log('2. Checking batch job status...');
    const batchStatus = await queueManager.getJobStatus(batchJobId);
    console.log(`✅ Batch status retrieved:`, batchStatus);
    
    return batchJobId;
    
  } catch (error) {
    console.error('❌ Batch processing test failed:', error);
    throw error;
  }
}

/**
 * Test retry functionality
 */
async function testRetryFunctionality() {
  console.log('\n=== Testing Retry Functionality ===');
  
  try {
    const retryOptions = {
      maxRetries: 2,
      retryDelay: 5, // 5 seconds
      exponentialBackoff: true
    };
    
    console.log('1. Queuing request with retry configuration...');
    const jobId = await queueManager.queueWithRetry(testPayload, retryOptions);
    
    console.log(`✅ Successfully queued retry job: ${jobId}`);
    
    return jobId;
    
  } catch (error) {
    console.error('❌ Retry functionality test failed:', error);
    throw error;
  }
}

/**
 * Test job monitoring
 */
async function testJobMonitoring(jobId) {
  console.log('\n=== Testing Job Monitoring ===');
  
  try {
    console.log(`1. Monitoring job: ${jobId}`);
    
    // Check if job is complete
    const isComplete = await isJobComplete(jobId);
    console.log(`✅ Job completion check: ${isComplete ? 'Complete' : 'In Progress'}`);
    
    // Get detailed status
    const detailedStatus = await queueManager.getJobStatus(jobId);
    console.log('✅ Detailed status:', detailedStatus);
    
    // Test waiting for completion (with short timeout for testing)
    console.log('2. Testing wait for completion (10 second timeout)...');
    try {
      const finalStatus = await waitForJobCompletion(jobId, 10000, 2000); // 10s timeout, 2s polling
      console.log('✅ Job completed:', finalStatus);
    } catch (timeoutError) {
      console.log('⏰ Job still in progress (timeout expected for testing)');
    }
    
  } catch (error) {
    console.error('❌ Job monitoring test failed:', error);
    throw error;
  }
}

/**
 * Test error handling
 */
async function testErrorHandling() {
  console.log('\n=== Testing Error Handling ===');
  
  try {
    // Test with invalid payload
    console.log('1. Testing invalid payload handling...');
    try {
      await queueLLMProcessing('', '', ''); // Empty values
      console.log('❌ Should have thrown error for empty values');
    } catch (error) {
      console.log('✅ Correctly handled invalid payload:', error.message);
    }
    
    // Test batch size limit
    console.log('2. Testing batch size limit...');
    try {
      const largeBatch = Array(51).fill(testPayload); // Exceeds 50 item limit
      await queueManager.queueBatchLLMRequests(largeBatch);
      console.log('❌ Should have thrown error for large batch');
    } catch (error) {
      console.log('✅ Correctly handled large batch:', error.message);
    }
    
    // Test invalid job ID
    console.log('3. Testing invalid job ID handling...');
    try {
      await queueManager.getJobStatus('invalid-job-id');
      console.log('❌ Should have thrown error for invalid job ID');
    } catch (error) {
      console.log('✅ Correctly handled invalid job ID:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error handling test failed:', error);
    throw error;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Async LLM Queue Tests\n');
  
  try {
    // Run basic tests
    const jobId = await testBasicQueue();
    
    // Run batch tests
    await testBatchProcessing();
    
    // Run retry tests
    await testRetryFunctionality();
    
    // Run monitoring tests
    await testJobMonitoring(jobId);
    
    // Run error handling tests
    await testErrorHandling();
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Test Summary:');
    console.log('✅ Basic queue functionality');
    console.log('✅ Batch processing');
    console.log('✅ Retry functionality');
    console.log('✅ Job monitoring');
    console.log('✅ Error handling');
    
  } catch (error) {
    console.error('\n💥 Test suite failed:', error);
    process.exit(1);
  }
}

/**
 * Display usage information
 */
function displayUsage() {
  console.log(`
📖 Async LLM Queue Test Script

This script tests the async queue functionality for LLM processing.

Usage:
  node test-async-queue.js

Note: This is a demonstration script. In a real Forge environment,
the queue operations would be handled by the Forge runtime.

The script tests:
- Basic queue operations
- Batch processing
- Retry mechanisms
- Job monitoring
- Error handling

For actual testing, deploy the app and trigger the functionality
through Jira issue updates.
`);
}

// Run tests if script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    displayUsage();
  } else {
    runAllTests().catch(console.error);
  }
}

export {
  testBasicQueue,
  testBatchProcessing,
  testRetryFunctionality,
  testJobMonitoring,
  testErrorHandling,
  runAllTests
};
