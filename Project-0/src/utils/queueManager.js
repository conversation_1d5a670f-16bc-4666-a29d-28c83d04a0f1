import { Queue } from '@forge/events';

/**
 * Queue Manager for LLM Processing
 * Provides utilities for managing async LLM processing jobs
 */
export class LLMQueueManager {
  constructor() {
    this.queue = new Queue({ key: 'llm-processing-queue' });
  }

  /**
   * Queue an LLM processing request
   * @param {Object} payload - The processing payload
   * @param {string} payload.issueId - Jira issue ID
   * @param {string} payload.issueKey - <PERSON><PERSON> issue key
   * @param {string} payload.simpleRequirements - Simple requirements text
   * @param {Object} options - Queue options
   * @param {number} options.delayInSeconds - Delay before processing (optional)
   * @returns {Promise<string>} Job ID
   */
  async queueLLMRequest(payload, options = {}) {
    try {
      const enhancedPayload = {
        ...payload,
        timestamp: new Date().toISOString(),
        version: '1.0',
        source: 'requirements-expander'
      };

      console.log('Queuing LLM request:', {
        issueKey: payload.issueKey,
        requirementsLength: payload.simpleRequirements?.length || 0,
        delay: options.delayInSeconds || 0
      });

      const jobId = await this.queue.push(enhancedPayload, options);
      
      console.log(`LLM processing job queued successfully: ${jobId}`);
      return jobId;

    } catch (error) {
      console.error('Failed to queue LLM request:', error);
      throw new Error(`Queue operation failed: ${error.message}`);
    }
  }

  /**
   * Get job status and statistics
   * @param {string} jobId - The job ID to check
   * @returns {Promise<Object>} Job statistics
   */
  async getJobStatus(jobId) {
    try {
      const jobProgress = this.queue.getJob(jobId);
      const response = await jobProgress.getStats();
      const stats = await response.json();
      
      console.log(`Job ${jobId} status:`, stats);
      return {
        jobId,
        ...stats,
        isComplete: stats.success + stats.failed === stats.success + stats.inProgress + stats.failed,
        hasErrors: stats.failed > 0
      };

    } catch (error) {
      console.error(`Failed to get job status for ${jobId}:`, error);
      throw new Error(`Status check failed: ${error.message}`);
    }
  }

  /**
   * Cancel a job in progress
   * @param {string} jobId - The job ID to cancel
   * @returns {Promise<boolean>} Success status
   */
  async cancelJob(jobId) {
    try {
      const jobProgress = this.queue.getJob(jobId);
      await jobProgress.cancel();
      
      console.log(`Job ${jobId} cancelled successfully`);
      return true;

    } catch (error) {
      console.error(`Failed to cancel job ${jobId}:`, error);
      return false;
    }
  }

  /**
   * Queue multiple LLM requests with batch processing
   * @param {Array<Object>} payloads - Array of processing payloads
   * @param {Object} options - Batch options
   * @returns {Promise<Array<string>>} Array of job IDs
   */
  async queueBatchLLMRequests(payloads, options = {}) {
    try {
      if (!Array.isArray(payloads) || payloads.length === 0) {
        throw new Error('Payloads must be a non-empty array');
      }

      if (payloads.length > 50) {
        throw new Error('Batch size cannot exceed 50 items');
      }

      console.log(`Queuing batch of ${payloads.length} LLM requests`);

      const enhancedPayloads = payloads.map((payload, index) => ({
        ...payload,
        timestamp: new Date().toISOString(),
        batchIndex: index,
        batchSize: payloads.length,
        version: '1.0',
        source: 'requirements-expander-batch'
      }));

      const jobId = await this.queue.push(enhancedPayloads, options);
      
      console.log(`Batch LLM processing job queued successfully: ${jobId}`);
      return jobId;

    } catch (error) {
      console.error('Failed to queue batch LLM requests:', error);
      throw new Error(`Batch queue operation failed: ${error.message}`);
    }
  }

  /**
   * Queue with retry logic for failed requests
   * @param {Object} payload - The processing payload
   * @param {Object} retryOptions - Retry configuration
   * @returns {Promise<string>} Job ID
   */
  async queueWithRetry(payload, retryOptions = {}) {
    const {
      maxRetries = 3,
      retryDelay = 60, // seconds
      exponentialBackoff = true
    } = retryOptions;

    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        const delay = attempt === 1 ? 0 : (
          exponentialBackoff 
            ? retryDelay * Math.pow(2, attempt - 2)
            : retryDelay
        );

        const enhancedPayload = {
          ...payload,
          retryAttempt: attempt,
          maxRetries,
          timestamp: new Date().toISOString()
        };

        return await this.queueLLMRequest(enhancedPayload, { delayInSeconds: delay });

      } catch (error) {
        lastError = error;
        console.warn(`Queue attempt ${attempt} failed:`, error.message);
        
        if (attempt === maxRetries + 1) {
          break;
        }
      }
    }

    throw new Error(`All ${maxRetries + 1} queue attempts failed. Last error: ${lastError.message}`);
  }
}

/**
 * Singleton instance for easy access
 */
export const queueManager = new LLMQueueManager();

/**
 * Helper function to queue a simple LLM request
 * @param {string} issueId - Jira issue ID
 * @param {string} issueKey - Jira issue key  
 * @param {string} simpleRequirements - Simple requirements text
 * @param {Object} context - Additional context
 * @returns {Promise<string>} Job ID
 */
export async function queueLLMProcessing(issueId, issueKey, simpleRequirements, context = {}) {
  const payload = {
    issueId,
    issueKey,
    simpleRequirements,
    triggerContext: context
  };

  return await queueManager.queueLLMRequest(payload);
}

/**
 * Helper function to check if a job is complete
 * @param {string} jobId - Job ID to check
 * @returns {Promise<boolean>} True if job is complete
 */
export async function isJobComplete(jobId) {
  try {
    const status = await queueManager.getJobStatus(jobId);
    return status.isComplete;
  } catch (error) {
    console.error('Error checking job completion:', error);
    return false;
  }
}

/**
 * Helper function to wait for job completion with timeout
 * @param {string} jobId - Job ID to wait for
 * @param {number} timeoutMs - Timeout in milliseconds (default: 5 minutes)
 * @param {number} pollIntervalMs - Polling interval in milliseconds (default: 10 seconds)
 * @returns {Promise<Object>} Final job status
 */
export async function waitForJobCompletion(jobId, timeoutMs = 300000, pollIntervalMs = 10000) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeoutMs) {
    try {
      const status = await queueManager.getJobStatus(jobId);
      
      if (status.isComplete) {
        return status;
      }
      
      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, pollIntervalMs));
      
    } catch (error) {
      console.error('Error polling job status:', error);
      // Continue polling despite errors
    }
  }
  
  throw new Error(`Job ${jobId} did not complete within ${timeoutMs}ms timeout`);
}
