import Resolver from '@forge/resolver';
import api, { route } from '@forge/api';
import { convertMarkdownToADF } from './utils/adf-converter.js';
import { InvocationError, InvocationErrorCode } from '@forge/events';

const resolver = new Resolver();

// Helper to get custom field ID - reused from main handler
const getCustomFieldId = async (fieldKey) => {
  try {
    const fieldsResponse = await api.asApp().requestJira(route`/rest/api/3/field`);
    if (!fieldsResponse.ok) {
      console.error(`Failed to fetch fields: ${fieldsResponse.status} ${fieldsResponse.statusText}`);
      return null;
    }

    const fieldsData = await fieldsResponse.json();
    const fieldNameMap = {
      'simple-requirements-field': 'Simple Requirements',
      'full-requirements-field': 'Full Requirements'
    };

    const targetFieldName = fieldNameMap[fieldKey];
    if (!targetFieldName) {
      console.error(`Unknown field key: ${fieldKey}`);
      return null;
    }

    const foundField = fieldsData.find(f => f.name === targetFieldName);
    if (!foundField) {
      console.error(`Could not find custom field with name: ${targetFieldName}`);
      return null;
    }

    console.log(`Found field ${targetFieldName} with ID: ${foundField.id}`);
    return foundField;
  } catch (error) {
    console.error(`Error fetching custom field ID for ${fieldKey}:`, error);
    return null;
  }
};

// Long-running LLM processing function
resolver.define('processLLMRequest', async ({ payload, context }) => {
  console.log('=== LLM Processor Started ===');
  console.log('Job ID:', context.jobId);
  console.log('Payload:', JSON.stringify(payload, null, 2));

  const { issueId, issueKey, simpleRequirements } = payload;

  // Check if this is a retry
  if (payload.retryContext) {
    const { retryCount, retryReason, retryData } = payload.retryContext;
    console.log(`🔄 Processing retry attempt ${retryCount} for issue ${issueKey}`);
    console.log(`Retry reason: ${retryReason}`);
    console.log(`Previous error: ${retryData?.error || 'Unknown'}`);
  }

  try {
    // Get OpenRouter API Key
    const apiKey = process.env.SECRET_OPENROUTER_API_KEY;
    if (!apiKey) {
      console.error('OpenRouter API Key not found in environment variables');
      return { success: false, error: 'API key not configured' };
    }

    // Get Full Requirements field information
    const fullReqField = await getCustomFieldId('full-requirements-field');
    if (!fullReqField) {
      console.error('Could not resolve Full Requirements field information');
      return { success: false, error: 'Full Requirements field not found' };
    }

    console.log(`Processing LLM request for issue ${issueKey}...`);
    console.log(`Simple Requirements: "${simpleRequirements}"`);

    // Enhanced prompt for better LLM results
    const prompt = `Expand the following simple requirements into detailed, actionable user story requirements:

"${simpleRequirements}"

Format the output using Markdown formatting.

Include:
- Clear acceptance criteria
- Technical requirements if applicable
- Implementation notes
- Any edge cases to consider`;

    let fullRequirements = '';

    // Enhanced retry configuration for long-running function
    const maxAttempts = 5; // More attempts since we have more time
    const baseDelay = 10000; // 10 seconds base delay
    const timeoutDuration = 120000; // 2 minutes per attempt

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`Calling OpenRouter API... (Attempt ${attempt}/${maxAttempts})`);

        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error(`OpenRouter API request timed out after ${timeoutDuration/1000} seconds`)), timeoutDuration);
        });

        // Create API request promise
        const apiRequestPromise = api.fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`,
            'HTTP-Referer': 'https://project0jiraplugin.atlassian.net',
            'X-Title': 'Jira Requirements Expander - Async',
          },
          body: JSON.stringify({
            model: 'deepseek/deepseek-chat-v3-0324:free',
            messages: [
              { role: 'system', content: 'You are a helpful assistant that expands requirements for Jira user stories. Provide detailed, well-structured requirements using Jira rich text formatting.' },
              { role: 'user', content: prompt },
            ],
            temperature: 0.7,
            max_tokens: 1000, // Increased token limit for more detailed responses
          }),
        });

        // Race between API request and timeout
        const openRouterResponse = await Promise.race([apiRequestPromise, timeoutPromise]);

        if (!openRouterResponse.ok) {
          const errorBody = await openRouterResponse.text();
          console.error(`OpenRouter API request failed (Attempt ${attempt}): ${openRouterResponse.status} ${openRouterResponse.statusText}`);
          console.error('Error Body:', errorBody);

          // Retry on rate limits or server errors
          if (openRouterResponse.status === 429 || openRouterResponse.status >= 500) {
            if (attempt < maxAttempts) {
              const delay = baseDelay * Math.pow(2, attempt - 1);
              console.log(`Retrying in ${delay/1000} seconds...`);
              await new Promise(resolve => setTimeout(resolve, delay));
              continue;
            }
          }

          throw new Error(`API request failed: ${openRouterResponse.status} ${openRouterResponse.statusText}`);
        }

        const openRouterData = await openRouterResponse.json();

        if (openRouterData.choices && openRouterData.choices.length > 0 && openRouterData.choices[0].message) {
          fullRequirements = openRouterData.choices[0].message.content.trim();
          console.log('OpenRouter Response Received:', fullRequirements.substring(0, 200) + '...');
          break; // Success, exit retry loop
        } else {
          console.error('OpenRouter response format unexpected:', JSON.stringify(openRouterData));
          if (attempt < maxAttempts) {
            const delay = baseDelay * Math.pow(2, attempt - 1);
            console.log(`Retrying in ${delay/1000} seconds due to unexpected response format...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
          throw new Error('Unexpected response format from OpenRouter API');
        }

      } catch (error) {
        console.error(`Error calling OpenRouter API (Attempt ${attempt}):`, error);

        if (attempt < maxAttempts) {
          const delay = baseDelay * Math.pow(2, attempt - 1);
          console.log(`Retrying in ${delay/1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        // Final attempt failed - use Forge retry mechanism for platform-level retries
        console.error(`All ${maxAttempts} attempts failed. Error:`, error.message);

        // Return InvocationError to trigger Forge platform retry
        return new InvocationError({
          retryAfter: 300, // Retry after 5 minutes
          retryReason: InvocationErrorCode.FUNCTION_RETRY_REQUEST,
          retryData: {
            issueKey,
            error: error.message,
            attemptCount: maxAttempts
          }
        });
      }
    }

    // Convert to ADF and update Jira issue
    console.log('Converting response to ADF format...');
    const adfContent = convertMarkdownToADF(fullRequirements);
    console.log('ADF Content Generated successfully');

    console.log(`Updating issue ${issueKey} with expanded requirements...`);
    const updateResponse = await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}`, {
      method: 'PUT',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fields: {
          [fullReqField.id]: adfContent
        },
      }),
    });

    if (!updateResponse.ok) {
      const errorBody = await updateResponse.text();
      console.error(`Failed to update issue ${issueKey}: ${updateResponse.status} ${updateResponse.statusText}`);
      console.error('Update Error Body:', errorBody);
      return { success: false, error: `Failed to update Jira issue: ${updateResponse.status}` };
    }

    console.log(`Successfully updated 'Full Requirements' field for issue ${issueKey}`);
    console.log('=== LLM Processor Completed Successfully ===');
    
    return { 
      success: true, 
      issueKey, 
      message: 'Requirements expanded and updated successfully',
      responseLength: fullRequirements.length
    };

  } catch (error) {
    console.error('=== LLM Processor Failed ===');
    console.error('Error:', error);
    return { success: false, error: error.message };
  }
});

export const handler = resolver.getDefinitions();
