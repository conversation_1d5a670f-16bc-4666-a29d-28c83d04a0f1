import api, { route } from '@forge/api'; // Import necessary Forge APIs
import { queueLLMProcessing } from '../utils/queueManager.js'; // Import queue management utilities

// --- Handler for Issue Update Trigger (Optimized for Simple Requirements Field) ---

// Helper to get custom field ID - uses API fallback since context may not have field info
const getCustomFieldId = async (fieldKey) => {
  try {
    // Query the API to get all fields and find our custom fields
    const fieldsResponse = await api.asApp().requestJira(route`/rest/api/3/field`);
    if (!fieldsResponse.ok) {
      console.error(`Failed to fetch fields: ${fieldsResponse.status} ${fieldsResponse.statusText}`);
      return null;
    }

    const fieldsData = await fieldsResponse.json();

    // Map field keys to field names
    const fieldNameMap = {
      'simple-requirements-field': 'Simple Requirements',
      'full-requirements-field': 'Full Requirements'
    };

    const targetFieldName = fieldNameMap[fieldKey];
    if (!targetFieldName) {
      console.error(`Unknown field key: ${fieldKey}`);
      return null;
    }

    // Find the field by name
    const foundField = fieldsData.find(f => f.name === targetFieldName);
    if (!foundField) {
      console.error(`Could not find custom field with name: ${targetFieldName}`);
      return null;
    }

    console.log(`Found field ${targetFieldName} with ID: ${foundField.id}, Type: ${foundField.schema?.type}, Custom: ${foundField.schema?.custom}`);
    return foundField;
  } catch (error) {
    console.error(`Error fetching custom field ID for ${fieldKey}:`, error);
    return null;
  }
};

export const simpleRequirementsUpdatedHandler = async (event, context) => {
  console.log('Issue Updated Event Received:', JSON.stringify(event));
  console.log('Context:', JSON.stringify(context));

  const issueId = event.issue.id;
  const issueKey = event.issue.key;

  console.log(`Processing issue ${issueKey} (ID: ${issueId})`);

  // 1. Get Custom Field information using API
  const simpleReqField = await getCustomFieldId('simple-requirements-field');
  if (!simpleReqField) {
    console.error('Could not resolve Simple Requirements field information');
    return;
  }

  // 2. Check if the Simple Requirements field was actually updated in this event
  const changelog = event.changelog;
  if (!changelog || !changelog.items) {
    console.log(`No changelog found in event. Skipping.`);
    return;
  }

  const simpleReqFieldUpdated = changelog.items.some(item =>
    item.fieldId === simpleReqField.id || item.field === 'Simple Requirements'
  );

  if (!simpleReqFieldUpdated) {
    console.log(`Simple Requirements field was not updated in this event. Skipping.`);
    return;
  }

  console.log(`Simple Requirements field was updated. Queuing for async processing...`);

  // 2. Get the new value of the Simple Requirements field from the changelog
  const simpleReqChange = changelog.items.find(item =>
    item.fieldId === simpleReqField.id || item.field === 'Simple Requirements'
  );

  const simpleRequirements = simpleReqChange?.to || '';
  console.log(`Simple Requirements Input: ${simpleRequirements}`);

  if (!simpleRequirements || simpleRequirements.trim() === '') {
    console.log(`Simple Requirements field is empty for issue ${issueKey}. Skipping.`);
    return;
  }

  // 3. Queue the LLM processing request for async execution
  try {
    const triggerContext = {
      eventId: context?.eventId || 'unknown',
      installationId: context?.installationId || 'unknown',
      timestamp: new Date().toISOString()
    };

    console.log('Queuing LLM processing request for async execution...');
    const jobId = await queueLLMProcessing(issueId, issueKey, simpleRequirements, triggerContext);

    console.log(`Successfully queued LLM processing job ${jobId} for issue ${issueKey}`);
    console.log('The issue will be updated asynchronously when processing completes.');
    console.log('Processing may take several minutes due to LLM API response times.');

    return {
      success: true,
      jobId,
      message: `LLM processing queued for issue ${issueKey}`,
      issueKey,
      estimatedCompletionTime: 'Within 5-10 minutes'
    };

  } catch (error) {
    console.error('Error queuing LLM processing request:', error);
    return {
      success: false,
      error: 'Failed to queue LLM processing request',
      details: error.message
    };
  }
};

// The simpleRequirementsUpdatedHandler is exported separately by name and referenced directly in manifest.yml
