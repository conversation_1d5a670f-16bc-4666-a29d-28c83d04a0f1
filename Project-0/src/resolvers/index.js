import api, { route } from '@forge/api'; // Import necessary Forge APIs
import { convertMarkdownToADF } from '../utils/adf-converter.js';

// --- Handler for Issue Update Trigger (Optimized for Simple Requirements Field) ---

// Helper to get custom field ID - uses API fallback since context may not have field info
const getCustomFieldId = async (fieldKey) => {
  try {
    // Query the API to get all fields and find our custom fields
    const fieldsResponse = await api.asApp().requestJira(route`/rest/api/3/field`);
    if (!fieldsResponse.ok) {
      console.error(`Failed to fetch fields: ${fieldsResponse.status} ${fieldsResponse.statusText}`);
      return null;
    }

    const fieldsData = await fieldsResponse.json();

    // Map field keys to field names
    const fieldNameMap = {
      'simple-requirements-field': 'Simple Requirements',
      'full-requirements-field': 'Full Requirements'
    };

    const targetFieldName = fieldNameMap[fieldKey];
    if (!targetFieldName) {
      console.error(`Unknown field key: ${fieldKey}`);
      return null;
    }

    // Find the field by name
    const foundField = fieldsData.find(f => f.name === targetFieldName);
    if (!foundField) {
      console.error(`Could not find custom field with name: ${targetFieldName}`);
      return null;
    }

    console.log(`Found field ${targetFieldName} with ID: ${foundField.id}, Type: ${foundField.schema?.type}, Custom: ${foundField.schema?.custom}`);
    return foundField;
  } catch (error) {
    console.error(`Error fetching custom field ID for ${fieldKey}:`, error);
    return null;
  }
};

export const simpleRequirementsUpdatedHandler = async (event, context) => {
  console.log('Issue Updated Event Received:', JSON.stringify(event));
  console.log('Context:', JSON.stringify(context));

  const issueId = event.issue.id;
  const issueKey = event.issue.key;

  console.log(`Processing issue ${issueKey} (ID: ${issueId})`);

  // 1. Get Custom Field information using API
  const simpleReqField = await getCustomFieldId('simple-requirements-field');
  if (!simpleReqField) {
    console.error('Could not resolve Simple Requirements field information');
    return;
  }

  // 2. Check if the Simple Requirements field was actually updated in this event
  const changelog = event.changelog;
  if (!changelog || !changelog.items) {
    console.log(`No changelog found in event. Skipping.`);
    return;
  }

  const simpleReqFieldUpdated = changelog.items.some(item =>
    item.fieldId === simpleReqField.id || item.field === 'Simple Requirements'
  );

  if (!simpleReqFieldUpdated) {
    console.log(`Simple Requirements field was not updated in this event. Skipping.`);
    return;
  }

  console.log(`Simple Requirements field was updated. Processing...`);

  // 2. Get OpenRouter API Key from Environment Variables
  const apiKey = process.env.SECRET_OPENROUTER_API_KEY;
  if (!apiKey) {
    console.error('OpenRouter API Key (SECRET_OPENROUTER_API_KEY) is not set in Forge environment variables. Ensure it was set using `forge variables set --encrypt SECRET_OPENROUTER_API_KEY`.');
    return;
  }

  // 3. Get Full Requirements field information using API
  const fullReqField = await getCustomFieldId('full-requirements-field');

  if (!fullReqField) {
     console.error(`Could not resolve Full Requirements field information: ${fullReqField}`);
     return;
  }
  console.log(`Field IDs - Simple: ${simpleReqField.id}, Full: ${fullReqField.id}`);
  console.log(`Field Types - Simple: ${simpleReqField.schema?.type}, Full: ${fullReqField.schema?.type}`);

  // 4. Get the new value of the Simple Requirements field from the changelog
  const simpleReqChange = changelog.items.find(item =>
    item.fieldId === simpleReqField.id || item.field === 'Simple Requirements'
  );

  const simpleRequirements = simpleReqChange?.to || '';
  console.log(`Simple Requirements Input: ${simpleRequirements}`);

  if (!simpleRequirements || simpleRequirements.trim() === '') {
    console.log(`Simple Requirements field is empty for issue ${issueKey}. Skipping.`);
    return;
  }

  // 5. Call OpenRouter API with DeepSeek V3 (with retry and exponential backoff)
  const prompt = `Expand the following simple requirements into detailed, actionable user story requirements:\n\n"${simpleRequirements}"\n\nFormat the output using Jira's rich text syntax. Use headers (h1., h2., h3.), bullet points (-), bold text (*bold*), italic text (_italic_), and other Jira formatting to make the requirements clear and well-structured. The output should be compatible with Jira's textarea field with rich text support.`;
  let fullRequirements = '';

  // Retry configuration (adjusted for Forge function timeout limits)
  const maxAttempts = 2;
  const baseDelay = 5000; // 5 seconds
  const timeoutDuration = 20000; // 20 seconds per attempt

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`Calling OpenRouter API with DeepSeek V3... (Attempt ${attempt}/${maxAttempts})`);

      // Create a timeout promise to prevent hanging
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`OpenRouter API request timed out after ${timeoutDuration/1000} seconds`)), timeoutDuration);
      });

      // Create the actual API request promise
      const apiRequestPromise = api.fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': 'https://project0jiraplugin.atlassian.net', // Optional: for analytics
          'X-Title': 'Jira Requirements Expander', // Optional: for analytics
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat-v3-0324:free', // DeepSeek V3 0324 free model
          messages: [
            { role: 'system', content: 'You are a helpful assistant that expands requirements for Jira user stories.' },
            { role: 'user', content: prompt },
          ],
          temperature: 0.7, // Adjust creativity
          max_tokens: 500, // Adjust response length limit
        }),
      });

      // Race between the API request and timeout
      const openRouterResponse = await Promise.race([apiRequestPromise, timeoutPromise]);

      if (!openRouterResponse.ok) {
        const errorBody = await openRouterResponse.text();
        console.error(`OpenRouter API request failed (Attempt ${attempt}): ${openRouterResponse.status} ${openRouterResponse.statusText}`);
        console.error('OpenRouter Error Body:', errorBody);

        // If it's a rate limit (429) or server error (5xx), retry
        if (openRouterResponse.status === 429 || openRouterResponse.status >= 500) {
          if (attempt < maxAttempts) {
            const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff: 2s, 4s, 8s
            console.log(`Retrying in ${delay/1000} seconds...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
        }

        // For other errors or final attempt, return
        return;
      }

      const openRouterData = await openRouterResponse.json();

      if (openRouterData.choices && openRouterData.choices.length > 0 && openRouterData.choices[0].message) {
          fullRequirements = openRouterData.choices[0].message.content.trim();
          console.log('OpenRouter Response Received (Jira Rich Text Format):', fullRequirements);
          break; // Success, exit retry loop
      } else {
          console.error('OpenRouter response format unexpected or empty.', JSON.stringify(openRouterData));
          if (attempt < maxAttempts) {
            const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff: 2s, 4s, 8s
            console.log(`Retrying in ${delay/1000} seconds due to unexpected response format...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }
          return;
      }

    } catch (error) {
      console.error(`Error calling OpenRouter API (Attempt ${attempt}):`, error);

      if (attempt < maxAttempts) {
        const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff: 2s, 4s, 8s
        console.log(`Retrying in ${delay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      // Final attempt failed
      console.error(`All ${maxAttempts} attempts failed. Giving up.`);
      return;
    }
  }
  
try {
    console.log(`Converting Markdown to ADF format...`);

    // Convert the Markdown response to ADF format
    const adfContent = convertMarkdownToADF(fullRequirements);
    console.log('ADF Content Generated:', JSON.stringify(adfContent, null, 2));

    console.log(`Updating issue ${issueKey} field ${fullReqField.id} with ADF content...`);

    // Use the standard issue update API with ADF format
    const updateResponse = await api.asApp().requestJira(route`/rest/api/3/issue/${issueId}`, {
      method: 'PUT',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        fields: {
          [fullReqField.id]: adfContent // ADF object for textarea custom field
        },
      }),
    });


    if (!updateResponse.ok) {
      const errorBody = await updateResponse.text();
      console.error(`Failed to update issue ${issueKey}: ${updateResponse.status} ${updateResponse.statusText}`);
      console.error('Update Error Body:', errorBody);
    } else {
      console.log(`Successfully updated 'Full Requirements' field (${fullReqField.id}) for issue ${issueKey} with rich text formatted content.`);
    }
  } catch (error) {
    console.error('Error updating Jira issue:', error);
  }
};

// The simpleRequirementsUpdatedHandler is exported separately by name and referenced directly in manifest.yml
