# Asynchronous LLM Processing Solution

## Overview

This document describes the implementation of an asynchronous solution to overcome Forge's 25-second function timeout limit when making LLM requests to OpenRouter.

## Problem Statement

- **Forge Limitation**: Standard Forge functions timeout after 25 seconds
- **LLM Response Times**: OpenRouter/LLM APIs often take 30+ seconds for quality responses
- **User Experience**: Issue updates were failing due to timeouts
- **Reliability**: No retry mechanism for failed LLM requests

## Solution Architecture

### Async Events API Implementation

The solution uses Forge's Async Events API to decouple the trigger from LLM processing:

```
Issue Update Trigger → Queue Event → Async Consumer (15min timeout) → Update Issue
     (< 1 second)      (instant)     (30+ seconds allowed)        (when complete)
```

### Key Components

1. **Trigger Handler** (`src/resolvers/index.js`)
   - Detects Simple Requirements field changes
   - Validates input and queues processing request
   - Returns immediately (non-blocking)

2. **Async LLM Processor** (`src/llmProcessor.js`)
   - Long-running function with 900-second (15-minute) timeout
   - Enhanced retry logic with exponential backoff
   - Improved error handling and logging

3. **Queue Manager** (`src/utils/queueManager.js`)
   - Utilities for queue management
   - Job status tracking and monitoring
   - Batch processing capabilities

## Configuration Changes

### Manifest Updates (`manifest.yml`)

```yaml
modules:
  consumer:
    - key: llm-processor-consumer
      queue: llm-processing-queue
      resolver:
        function: llm-processor-function
        method: processLLMRequest
  function:
    - key: llm-processor-function
      handler: llmProcessor.handler
      timeoutSeconds: 900  # 15 minutes
```

### Dependencies

Added `@forge/events` package for async queue functionality.

## Benefits

### 1. **No More Timeouts**
- LLM processing can take up to 15 minutes
- No more 25-second limit constraints
- Better handling of slow API responses

### 2. **Non-Blocking Updates**
- Issue updates happen immediately when field is changed
- Users don't wait for LLM processing to complete
- Better user experience

### 3. **Enhanced Reliability**
- 5 retry attempts with exponential backoff
- Better error handling and recovery
- Detailed logging for troubleshooting

### 4. **Improved Performance**
- Increased token limit (1000 vs 500)
- Longer timeout per attempt (2 minutes vs 20 seconds)
- More sophisticated retry strategy

### 5. **Monitoring & Management**
- Job status tracking
- Progress monitoring
- Ability to cancel jobs
- Batch processing support

## Usage Examples

### Basic Queue Operation

```javascript
import { queueLLMProcessing } from './utils/queueManager.js';

// Queue a processing request
const jobId = await queueLLMProcessing(
  issueId, 
  issueKey, 
  simpleRequirements, 
  context
);

console.log(`Job queued: ${jobId}`);
```

### Job Status Monitoring

```javascript
import { queueManager } from './utils/queueManager.js';

// Check job status
const status = await queueManager.getJobStatus(jobId);
console.log('Job status:', status);

// Wait for completion
const finalStatus = await waitForJobCompletion(jobId);
```

### Batch Processing

```javascript
// Process multiple issues
const payloads = [
  { issueId: '1', issueKey: 'PROJ-1', simpleRequirements: 'req1' },
  { issueId: '2', issueKey: 'PROJ-2', simpleRequirements: 'req2' }
];

const batchJobId = await queueManager.queueBatchLLMRequests(payloads);
```

## Error Handling

### Retry Strategy

1. **5 retry attempts** (increased from 2)
2. **Exponential backoff**: 10s, 20s, 40s, 80s, 160s
3. **Per-attempt timeout**: 2 minutes (increased from 20 seconds)
4. **Total possible time**: Up to 15 minutes

### Error Types Handled

- **Rate Limiting (429)**: Automatic retry with backoff
- **Server Errors (5xx)**: Automatic retry
- **Timeout Errors**: Retry with longer timeout
- **Network Issues**: Retry with exponential backoff
- **API Format Errors**: Logged and reported

## Monitoring & Debugging

### Logging

Enhanced logging throughout the process:

```
=== LLM Processor Started ===
Job ID: queue-name#abc123
Processing LLM request for issue PROJ-123...
Calling OpenRouter API... (Attempt 1/5)
OpenRouter Response Received: ...
Converting response to ADF format...
Updating issue PROJ-123 with expanded requirements...
=== LLM Processor Completed Successfully ===
```

### Job Tracking

```javascript
// Get detailed job statistics
const stats = await queueManager.getJobStatus(jobId);
// Returns: { success: 0, inProgress: 1, failed: 0, isComplete: false }
```

## Deployment

### Prerequisites
- Forge CLI installed and configured
- OpenRouter API key set as environment variable
- Jira instance with custom fields configured

### Step-by-Step Deployment

#### 1. Install Dependencies
```bash
cd Project-0
npm install @forge/events
```

#### 2. Verify Environment Variables
```bash
forge variables list
# Should show SECRET_OPENROUTER_API_KEY

# If not set:
forge variables set --encrypt SECRET_OPENROUTER_API_KEY
# Enter your OpenRouter API key when prompted
```

#### 3. Validate Configuration
```bash
# Check for syntax errors
forge lint

# Validate manifest
cat manifest.yml
```

#### 4. Deploy to Development
```bash
forge deploy --environment development
```

#### 5. Test in Development
- Update a Simple Requirements field in your dev Jira instance
- Check logs: `forge logs --environment development`
- Verify async processing works

#### 6. Deploy to Production
```bash
forge deploy --environment production
```

### Post-Deployment Verification

#### 1. Check App Status
```bash
forge install list
# Verify app is installed and running
```

#### 2. Monitor Initial Requests
```bash
forge logs --tail
# Watch for queue and processing logs
```

#### 3. Test End-to-End Flow
1. Go to a Jira issue
2. Update the "Simple Requirements" field
3. Check logs for queue confirmation
4. Wait for "Full Requirements" field to be updated
5. Verify the expanded content is properly formatted

## Testing

### Manual Testing

1. Update a Simple Requirements field in Jira
2. Check logs for queue confirmation
3. Monitor async processing logs
4. Verify Full Requirements field is updated

### Expected Log Flow

```
Issue Updated Event Received: {...}
Simple Requirements field was updated. Queuing for async processing...
Successfully queued LLM processing job queue-name#abc123 for issue PROJ-123
=== LLM Processor Started ===
[... processing logs ...]
=== LLM Processor Completed Successfully ===
```

## Performance Improvements

| Metric | Before | After | Improvement |
|--------|--------|-------|-------------|
| Timeout Limit | 25 seconds | 15 minutes | 36x increase |
| Retry Attempts | 2 | 5 | 2.5x increase |
| Per-attempt Timeout | 20 seconds | 2 minutes | 6x increase |
| Token Limit | 500 | 1000 | 2x increase |
| User Wait Time | 25+ seconds | < 1 second | 25x faster |

## Future Enhancements

1. **Progress Notifications**: Update issue with processing status
2. **Priority Queuing**: High-priority issues processed first
3. **Batch Optimization**: Process multiple requirements together
4. **Caching**: Cache similar requirements to reduce API calls
5. **Analytics**: Track processing times and success rates

## Troubleshooting

### Common Issues

1. **Queue not processing**: Check consumer configuration in manifest
2. **Jobs timing out**: Verify 900-second timeout is set
3. **API key issues**: Ensure SECRET_OPENROUTER_API_KEY is set
4. **Field not updating**: Check ADF conversion and field permissions

### Debug Commands

```bash
# View logs
forge logs

# Check environment variables
forge variables list

# Redeploy if needed
forge deploy
```

## Conclusion

This asynchronous solution completely eliminates the 25-second timeout limitation while providing a better user experience and more reliable LLM processing. The implementation is production-ready with comprehensive error handling, monitoring, and retry mechanisms.
