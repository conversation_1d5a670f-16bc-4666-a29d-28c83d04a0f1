modules:
  jira:customField:
    - key: simple-requirements-field
      name: Simple Requirements
      description: Input field for simple requirements for LLM expansion.
      type: string
      readOnly: false # Ensure it's editable
  consumer:
    - key: llm-processor-consumer
      queue: llm-processing-queue
      resolver:
        function: llm-processor-function
        method: processLLMRequest
  function:
    - key: field-updated-handler # Function for the trigger
      handler: index.simpleRequirementsUpdatedHandler
    - key: llm-processor-function # Long-running function for LLM processing
      handler: llmProcessor.handler
      timeoutSeconds: 900 # 15 minutes timeout for LLM processing
  trigger: # Trigger module
    - key: field-updated-trigger
      function: field-updated-handler
      events:
        - avi:jira:updated:issue

app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/1855cc49-eb1c-46b8-b8d7-04e25dbd5aa8
permissions: # Added permissions section
  scopes:
    - read:jira-work # To read issue details and custom fields
    - write:jira-work # To update custom fields
  external:
    fetch:
      backend:
        - https://openrouter.ai # Allow calls to OpenRouter API
